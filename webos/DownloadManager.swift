//
//  DownloadManager.swift
//  webos
//
//  Created by lincoo on 7/3/25.
//

import SwiftUI
import Foundation

enum DownloadStatus {
    case waiting
    case downloading
    case completed
    case failed
    case cancelled
}

class DownloadItem: ObservableObject, Identifiable {
    let id = UUID()
    @Published var fileName: String
    @Published var url: String
    @Published var progress: Double = 0.0
    @Published var status: DownloadStatus = .waiting
    @Published var fileSize: Int64 = 0
    @Published var downloadedSize: Int64 = 0
    @Published var downloadSpeed: Double = 0.0
    @Published var remainingTime: TimeInterval = 0
    @Published var localPath: URL?
    
    var task: URLSessionDownloadTask?
    
    init(fileName: String, url: String) {
        self.fileName = fileName
        self.url = url
    }
}

class DownloadManager: NSObject, ObservableObject {
    @Published var downloads: [DownloadItem] = []
    @Published var showDownloads = false
    
    private var urlSession: URLSession!
    private let downloadDirectory: URL
    
    override init() {
        // 设置下载目录
        let documentsPath = FileManager.default.urls(for: .downloadsDirectory, in: .userDomainMask).first!
        downloadDirectory = documentsPath.appendingPathComponent("WebOS Downloads")
        
        super.init()
        
        // 创建下载目录
        try? FileManager.default.createDirectory(at: downloadDirectory, withIntermediateDirectories: true)
        
        // 配置URLSession
        let config = URLSessionConfiguration.default
        urlSession = URLSession(configuration: config, delegate: self, delegateQueue: nil)
    }
    
    func startDownload(url: String, fileName: String? = nil) {
        guard let downloadURL = URL(string: url) else { return }
        
        let finalFileName = fileName ?? downloadURL.lastPathComponent
        let downloadItem = DownloadItem(fileName: finalFileName, url: url)
        
        DispatchQueue.main.async {
            self.downloads.insert(downloadItem, at: 0)
        }
        
        let task = urlSession.downloadTask(with: downloadURL)
        downloadItem.task = task
        downloadItem.status = .downloading
        
        task.resume()
    }
    
    func pauseDownload(_ item: DownloadItem) {
        item.task?.suspend()
        item.status = .waiting
    }
    
    func resumeDownload(_ item: DownloadItem) {
        item.task?.resume()
        item.status = .downloading
    }
    
    func cancelDownload(_ item: DownloadItem) {
        item.task?.cancel()
        item.status = .cancelled
    }
    
    func removeDownload(_ item: DownloadItem) {
        item.task?.cancel()
        DispatchQueue.main.async {
            self.downloads.removeAll { $0.id == item.id }
        }
    }
    
    func openDownloadedFile(_ item: DownloadItem) {
        guard let localPath = item.localPath else { return }
        NSWorkspace.shared.open(localPath)
    }
    
    func showInFinder(_ item: DownloadItem) {
        guard let localPath = item.localPath else { return }
        NSWorkspace.shared.selectFile(localPath.path, inFileViewerRootedAtPath: "")
    }
    
    private func formatFileSize(_ bytes: Int64) -> String {
        let formatter = ByteCountFormatter()
        formatter.allowedUnits = [.useKB, .useMB, .useGB]
        formatter.countStyle = .file
        return formatter.string(fromByteCount: bytes)
    }
    
    private func formatSpeed(_ bytesPerSecond: Double) -> String {
        let formatter = ByteCountFormatter()
        formatter.allowedUnits = [.useKB, .useMB, .useGB]
        formatter.countStyle = .file
        return formatter.string(fromByteCount: Int64(bytesPerSecond)) + "/s"
    }
    
    private func formatTime(_ seconds: TimeInterval) -> String {
        if seconds.isInfinite || seconds.isNaN {
            return "未知"
        }
        
        let hours = Int(seconds) / 3600
        let minutes = Int(seconds) % 3600 / 60
        let secs = Int(seconds) % 60
        
        if hours > 0 {
            return String(format: "%d:%02d:%02d", hours, minutes, secs)
        } else {
            return String(format: "%02d:%02d", minutes, secs)
        }
    }
}

extension DownloadManager: URLSessionDownloadDelegate {
    func urlSession(_ session: URLSession, downloadTask: URLSessionDownloadTask, didFinishDownloadingTo location: URL) {
        guard let downloadItem = downloads.first(where: { $0.task == downloadTask }) else { return }
        
        let destinationURL = downloadDirectory.appendingPathComponent(downloadItem.fileName)
        
        do {
            // 如果文件已存在，删除它
            if FileManager.default.fileExists(atPath: destinationURL.path) {
                try FileManager.default.removeItem(at: destinationURL)
            }
            
            // 移动文件到下载目录
            try FileManager.default.moveItem(at: location, to: destinationURL)
            
            DispatchQueue.main.async {
                downloadItem.localPath = destinationURL
                downloadItem.status = .completed
                downloadItem.progress = 1.0
            }
        } catch {
            DispatchQueue.main.async {
                downloadItem.status = .failed
            }
        }
    }
    
    func urlSession(_ session: URLSession, downloadTask: URLSessionDownloadTask, didWriteData bytesWritten: Int64, totalBytesWritten: Int64, totalBytesExpectedToWrite: Int64) {
        guard let downloadItem = downloads.first(where: { $0.task == downloadTask }) else { return }
        
        DispatchQueue.main.async {
            downloadItem.fileSize = totalBytesExpectedToWrite
            downloadItem.downloadedSize = totalBytesWritten
            downloadItem.progress = Double(totalBytesWritten) / Double(totalBytesExpectedToWrite)
            
            // 计算下载速度和剩余时间
            let currentTime = Date().timeIntervalSince1970
            let speed = Double(bytesWritten) / 1.0 // 假设每秒更新一次
            downloadItem.downloadSpeed = speed
            
            let remainingBytes = totalBytesExpectedToWrite - totalBytesWritten
            if speed > 0 {
                downloadItem.remainingTime = Double(remainingBytes) / speed
            }
        }
    }
    
    func urlSession(_ session: URLSession, task: URLSessionTask, didCompleteWithError error: Error?) {
        guard let downloadItem = downloads.first(where: { $0.task == task }) else { return }
        
        if let error = error {
            DispatchQueue.main.async {
                downloadItem.status = .failed
            }
        }
    }
}

struct DownloadManagerView: View {
    @ObservedObject var downloadManager: DownloadManager
    @Environment(\.presentationMode) var presentationMode
    
    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            // 标题栏
            HStack {
                Text("下载管理")
                    .font(.title2)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Button("清除已完成") {
                    downloadManager.downloads.removeAll { $0.status == .completed }
                }
                
                Button("完成") {
                    presentationMode.wrappedValue.dismiss()
                }
            }
            .padding()
            
            Divider()
            
            // 下载列表
            if downloadManager.downloads.isEmpty {
                VStack {
                    Spacer()
                    Image(systemName: "arrow.down.circle")
                        .font(.system(size: 48))
                        .foregroundColor(.secondary)
                    Text("暂无下载")
                        .font(.headline)
                        .foregroundColor(.secondary)
                    Spacer()
                }
            } else {
                ScrollView {
                    LazyVStack(spacing: 0) {
                        ForEach(downloadManager.downloads) { item in
                            DownloadItemView(
                                item: item,
                                onPause: { downloadManager.pauseDownload(item) },
                                onResume: { downloadManager.resumeDownload(item) },
                                onCancel: { downloadManager.cancelDownload(item) },
                                onRemove: { downloadManager.removeDownload(item) },
                                onOpen: { downloadManager.openDownloadedFile(item) },
                                onShowInFinder: { downloadManager.showInFinder(item) }
                            )
                            
                            if item.id != downloadManager.downloads.last?.id {
                                Divider()
                                    .padding(.horizontal)
                            }
                        }
                    }
                }
            }
        }
        .frame(width: 600, height: 400)
    }
}

struct DownloadItemView: View {
    @ObservedObject var item: DownloadItem
    
    let onPause: () -> Void
    let onResume: () -> Void
    let onCancel: () -> Void
    let onRemove: () -> Void
    let onOpen: () -> Void
    let onShowInFinder: () -> Void
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(item.fileName)
                        .font(.system(size: 14, weight: .medium))
                        .lineLimit(1)
                    
                    Text(item.url)
                        .font(.system(size: 12))
                        .foregroundColor(.secondary)
                        .lineLimit(1)
                }
                
                Spacer()
                
                // 控制按钮
                HStack(spacing: 8) {
                    switch item.status {
                    case .downloading:
                        Button(action: onPause) {
                            Image(systemName: "pause.circle")
                        }
                    case .waiting:
                        Button(action: onResume) {
                            Image(systemName: "play.circle")
                        }
                    case .completed:
                        Button(action: onOpen) {
                            Image(systemName: "doc.circle")
                        }
                        Button(action: onShowInFinder) {
                            Image(systemName: "folder.circle")
                        }
                    case .failed, .cancelled:
                        Button(action: onResume) {
                            Image(systemName: "arrow.clockwise.circle")
                        }
                    }
                    
                    Button(action: onRemove) {
                        Image(systemName: "xmark.circle")
                            .foregroundColor(.red)
                    }
                }
                .buttonStyle(PlainButtonStyle())
            }
            
            // 进度信息
            if item.status == .downloading || item.status == .waiting {
                VStack(alignment: .leading, spacing: 4) {
                    ProgressView(value: item.progress)
                        .progressViewStyle(LinearProgressViewStyle())
                    
                    HStack {
                        Text("\(Int(item.progress * 100))%")
                            .font(.system(size: 12))
                            .foregroundColor(.secondary)
                        
                        Spacer()
                        
                        if item.downloadSpeed > 0 {
                            Text(formatSpeed(item.downloadSpeed))
                                .font(.system(size: 12))
                                .foregroundColor(.secondary)
                        }
                        
                        if item.remainingTime > 0 && !item.remainingTime.isInfinite {
                            Text("剩余 \(formatTime(item.remainingTime))")
                                .font(.system(size: 12))
                                .foregroundColor(.secondary)
                        }
                    }
                }
            }
            
            // 状态信息
            HStack {
                statusText
                Spacer()
                if item.fileSize > 0 {
                    Text(formatFileSize(item.fileSize))
                        .font(.system(size: 12))
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding()
    }
    
    @ViewBuilder
    private var statusText: some View {
        switch item.status {
        case .waiting:
            Text("等待中")
                .font(.system(size: 12))
                .foregroundColor(.orange)
        case .downloading:
            Text("下载中")
                .font(.system(size: 12))
                .foregroundColor(.blue)
        case .completed:
            Text("已完成")
                .font(.system(size: 12))
                .foregroundColor(.green)
        case .failed:
            Text("下载失败")
                .font(.system(size: 12))
                .foregroundColor(.red)
        case .cancelled:
            Text("已取消")
                .font(.system(size: 12))
                .foregroundColor(.secondary)
        }
    }
    
    private func formatFileSize(_ bytes: Int64) -> String {
        let formatter = ByteCountFormatter()
        formatter.allowedUnits = [.useKB, .useMB, .useGB]
        formatter.countStyle = .file
        return formatter.string(fromByteCount: bytes)
    }
    
    private func formatSpeed(_ bytesPerSecond: Double) -> String {
        let formatter = ByteCountFormatter()
        formatter.allowedUnits = [.useKB, .useMB, .useGB]
        formatter.countStyle = .file
        return formatter.string(fromByteCount: Int64(bytesPerSecond)) + "/s"
    }
    
    private func formatTime(_ seconds: TimeInterval) -> String {
        let hours = Int(seconds) / 3600
        let minutes = Int(seconds) % 3600 / 60
        let secs = Int(seconds) % 60
        
        if hours > 0 {
            return String(format: "%d:%02d:%02d", hours, minutes, secs)
        } else {
            return String(format: "%02d:%02d", minutes, secs)
        }
    }
}
