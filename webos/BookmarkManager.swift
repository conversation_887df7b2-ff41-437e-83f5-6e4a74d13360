//
//  BookmarkManager.swift
//  webos
//
//  Created by lincoo on 7/3/25.
//

import SwiftUI
import Foundation

struct Bookmark: Identifiable, Codable {
    let id = UUID()
    var title: String
    var url: String
    var favicon: Data?
    var dateAdded: Date
    var isFolder: Bool
    var children: [Bookmark]?
    
    init(title: String, url: String, favicon: Data? = nil, isFolder: Bool = false, children: [Bookmark]? = nil) {
        self.title = title
        self.url = url
        self.favicon = favicon
        self.dateAdded = Date()
        self.isFolder = isFolder
        self.children = children
    }
}

class BookmarkManager: ObservableObject {
    @Published var bookmarks: [Bookmark] = []
    @Published var bookmarkBarItems: [Bookmark] = []
    @Published var showBookmarkBar: Bool = true
    
    private let bookmarksKey = "SavedBookmarks"
    private let bookmarkBarKey = "BookmarkBarItems"
    private let showBookmarkBarKey = "ShowBookmarkBar"
    
    init() {
        loadBookmarks()
        loadBookmarkBarItems()
        loadBookmarkBarVisibility()
        
        // 添加一些默认书签
        if bookmarkBarItems.isEmpty {
            addDefaultBookmarks()
        }
    }
    
    private func addDefaultBookmarks() {
        let defaultBookmarks = [
            Bookmark(title: "Apple", url: "https://www.apple.com"),
            Bookmark(title: "Google", url: "https://www.google.com"),
            Bookmark(title: "GitHub", url: "https://github.com"),
            Bookmark(title: "Stack Overflow", url: "https://stackoverflow.com")
        ]
        
        bookmarkBarItems = defaultBookmarks
        saveBookmarkBarItems()
    }
    
    func addBookmark(title: String, url: String, toBookmarkBar: Bool = false) {
        let bookmark = Bookmark(title: title, url: url)
        
        if toBookmarkBar {
            bookmarkBarItems.append(bookmark)
            saveBookmarkBarItems()
        } else {
            bookmarks.append(bookmark)
            saveBookmarks()
        }
    }
    
    func removeBookmark(_ bookmark: Bookmark, fromBookmarkBar: Bool = false) {
        if fromBookmarkBar {
            bookmarkBarItems.removeAll { $0.id == bookmark.id }
            saveBookmarkBarItems()
        } else {
            bookmarks.removeAll { $0.id == bookmark.id }
            saveBookmarks()
        }
    }
    
    func isBookmarked(url: String) -> Bool {
        return bookmarks.contains { $0.url == url } || bookmarkBarItems.contains { $0.url == url }
    }
    
    func toggleBookmarkBar() {
        showBookmarkBar.toggle()
        saveBookmarkBarVisibility()
    }
    
    private func saveBookmarks() {
        if let data = try? JSONEncoder().encode(bookmarks) {
            UserDefaults.standard.set(data, forKey: bookmarksKey)
        }
    }
    
    private func loadBookmarks() {
        if let data = UserDefaults.standard.data(forKey: bookmarksKey),
           let savedBookmarks = try? JSONDecoder().decode([Bookmark].self, from: data) {
            bookmarks = savedBookmarks
        }
    }
    
    private func saveBookmarkBarItems() {
        if let data = try? JSONEncoder().encode(bookmarkBarItems) {
            UserDefaults.standard.set(data, forKey: bookmarkBarKey)
        }
    }
    
    private func loadBookmarkBarItems() {
        if let data = UserDefaults.standard.data(forKey: bookmarkBarKey),
           let savedItems = try? JSONDecoder().decode([Bookmark].self, from: data) {
            bookmarkBarItems = savedItems
        }
    }
    
    private func saveBookmarkBarVisibility() {
        UserDefaults.standard.set(showBookmarkBar, forKey: showBookmarkBarKey)
    }
    
    private func loadBookmarkBarVisibility() {
        showBookmarkBar = UserDefaults.standard.bool(forKey: showBookmarkBarKey)
    }
}

struct BookmarkBar: View {
    @ObservedObject var bookmarkManager: BookmarkManager
    let onNavigate: (String) -> Void
    
    var body: some View {
        if bookmarkManager.showBookmarkBar {
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 8) {
                    ForEach(bookmarkManager.bookmarkBarItems) { bookmark in
                        BookmarkButton(bookmark: bookmark, onNavigate: onNavigate)
                    }
                }
                .padding(.horizontal, 12)
            }
            .frame(height: 32)
            .background(Color(NSColor.controlBackgroundColor))
        }
    }
}

struct BookmarkButton: View {
    let bookmark: Bookmark
    let onNavigate: (String) -> Void
    
    @State private var isHovering = false
    
    var body: some View {
        Button(action: {
            onNavigate(bookmark.url)
        }) {
            HStack(spacing: 4) {
                Image(systemName: "globe")
                    .font(.system(size: 12))
                    .foregroundColor(.secondary)
                
                Text(bookmark.title)
                    .font(.system(size: 12))
                    .foregroundColor(.primary)
                    .lineLimit(1)
            }
            .padding(.horizontal, 8)
            .padding(.vertical, 4)
            .background(
                RoundedRectangle(cornerRadius: 4)
                    .fill(isHovering ? Color(NSColor.controlAccentColor).opacity(0.1) : Color.clear)
            )
        }
        .buttonStyle(PlainButtonStyle())
        .onHover { hovering in
            isHovering = hovering
        }
        .contextMenu {
            Button("在新标签页中打开") {
                // TODO: 在新标签页中打开
            }
            
            Divider()
            
            Button("编辑书签") {
                // TODO: 编辑书签
            }
            
            Button("删除书签") {
                // TODO: 删除书签
            }
        }
    }
}

struct BookmarkManagerView: View {
    @ObservedObject var bookmarkManager: BookmarkManager
    @Environment(\.presentationMode) var presentationMode
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("书签管理")
                    .font(.title2)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Button("完成") {
                    presentationMode.wrappedValue.dismiss()
                }
            }
            
            Toggle("显示书签栏", isOn: $bookmarkManager.showBookmarkBar)
            
            Divider()
            
            Text("书签栏")
                .font(.headline)
            
            List {
                ForEach(bookmarkManager.bookmarkBarItems) { bookmark in
                    HStack {
                        Image(systemName: "globe")
                            .foregroundColor(.secondary)
                        
                        VStack(alignment: .leading) {
                            Text(bookmark.title)
                                .font(.system(size: 14))
                            Text(bookmark.url)
                                .font(.system(size: 12))
                                .foregroundColor(.secondary)
                        }
                        
                        Spacer()
                        
                        Button(action: {
                            bookmarkManager.removeBookmark(bookmark, fromBookmarkBar: true)
                        }) {
                            Image(systemName: "trash")
                                .foregroundColor(.red)
                        }
                        .buttonStyle(PlainButtonStyle())
                    }
                    .padding(.vertical, 4)
                }
            }
            .frame(minHeight: 200)
            
            Spacer()
        }
        .padding()
        .frame(width: 500, height: 400)
    }
}
