//
//  ToolbarView.swift
//  webos
//
//  Created by lincoo on 7/3/25.
//

import SwiftUI

struct ToolbarView: View {
    @ObservedObject var tabManager: TabManager
    @ObservedObject var bookmarkManager: BookmarkManager
    @ObservedObject var historyManager: HistoryManager
    @ObservedObject var downloadManager: DownloadManager
    
    @Binding var showBookmarkManager: Bool
    @Binding var showHistoryView: Bool
    @Binding var showDownloadManager: Bool
    
    let onNavigate: (String) -> Void
    
    var body: some View {
        HStack(spacing: 12) {
            // 主页按钮
            Button(action: {
                onNavigate("https://www.apple.com")
            }) {
                Image(systemName: "house")
                    .font(.system(size: 16))
            }
            .buttonStyle(ToolbarButtonStyle())
            .help("主页")
            
            Divider()
                .frame(height: 20)
            
            // 书签按钮
            Button(action: {
                if let activeTab = tabManager.activeTab {
                    if bookmarkManager.isBookmarked(url: activeTab.url) {
                        // 如果已经是书签，显示书签管理器
                        showBookmarkManager = true
                    } else {
                        // 添加书签
                        bookmarkManager.addBookmark(title: activeTab.title, url: activeTab.url, toBookmarkBar: true)
                    }
                }
            }) {
                Image(systemName: isCurrentPageBookmarked ? "star.fill" : "star")
                    .font(.system(size: 16))
                    .foregroundColor(isCurrentPageBookmarked ? .yellow : .primary)
            }
            .buttonStyle(ToolbarButtonStyle())
            .help(isCurrentPageBookmarked ? "已添加书签" : "添加书签")
            
            // 书签管理器按钮
            Button(action: {
                showBookmarkManager = true
            }) {
                Image(systemName: "book")
                    .font(.system(size: 16))
            }
            .buttonStyle(ToolbarButtonStyle())
            .help("书签管理器")
            
            // 历史记录按钮
            Button(action: {
                showHistoryView = true
            }) {
                Image(systemName: "clock")
                    .font(.system(size: 16))
            }
            .buttonStyle(ToolbarButtonStyle())
            .help("历史记录")
            
            // 下载管理器按钮
            Button(action: {
                showDownloadManager = true
            }) {
                ZStack {
                    Image(systemName: "arrow.down.circle")
                        .font(.system(size: 16))
                    
                    // 下载数量徽章
                    if downloadManager.downloads.filter({ $0.status == .downloading }).count > 0 {
                        Circle()
                            .fill(Color.red)
                            .frame(width: 8, height: 8)
                            .offset(x: 8, y: -8)
                    }
                }
            }
            .buttonStyle(ToolbarButtonStyle())
            .help("下载管理器")
            
            Divider()
                .frame(height: 20)
            
            // 书签栏切换按钮
            Button(action: {
                bookmarkManager.toggleBookmarkBar()
            }) {
                Image(systemName: bookmarkManager.showBookmarkBar ? "sidebar.left" : "sidebar.left.slash")
                    .font(.system(size: 16))
            }
            .buttonStyle(ToolbarButtonStyle())
            .help(bookmarkManager.showBookmarkBar ? "隐藏书签栏" : "显示书签栏")
            
            Spacer()
            
            // 设置按钮
            Menu {
                Button("新建标签页") {
                    tabManager.addNewTab()
                }
                
                Button("新建窗口") {
                    // TODO: 实现新建窗口
                }
                
                Divider()
                
                Button("打印页面") {
                    // TODO: 实现打印功能
                }
                
                Button("查看页面源代码") {
                    // TODO: 实现查看源代码
                }
                
                Divider()
                
                Button("设置") {
                    // TODO: 实现设置页面
                }
                
                Button("关于 WebOS") {
                    // TODO: 实现关于页面
                }
            } label: {
                Image(systemName: "ellipsis.circle")
                    .font(.system(size: 16))
            }
            .buttonStyle(ToolbarButtonStyle())
            .help("更多选项")
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 6)
        .background(Color(NSColor.windowBackgroundColor))
    }
    
    private var isCurrentPageBookmarked: Bool {
        guard let activeTab = tabManager.activeTab else { return false }
        return bookmarkManager.isBookmarked(url: activeTab.url)
    }
}

struct ToolbarButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .frame(width: 32, height: 32)
            .background(
                RoundedRectangle(cornerRadius: 6)
                    .fill(configuration.isPressed ? Color(NSColor.controlAccentColor).opacity(0.2) : Color.clear)
            )
            .scaleEffect(configuration.isPressed ? 0.95 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
    }
}

#Preview {
    ToolbarView(
        tabManager: TabManager(),
        bookmarkManager: BookmarkManager(),
        historyManager: HistoryManager(),
        downloadManager: DownloadManager(),
        showBookmarkManager: .constant(false),
        showHistoryView: .constant(false),
        showDownloadManager: .constant(false),
        onNavigate: { _ in }
    )
}
