//
//  webosApp.swift
//  webos
//
//  Created by lincoo on 7/3/25.
//

import SwiftUI

@main
struct webosApp: App {
    var body: some Scene {
        WindowGroup {
            ContentView()
        }
        .windowStyle(.titleBar)
        .windowToolbarStyle(.unified)
        .commands {
            BrowserCommands()
        }
    }
}

struct BrowserCommands: Commands {
    var body: some Commands {
        CommandGroup(after: .newItem) {
            But<PERSON>("新建标签页") {
                // TODO: 实现新建标签页
            }
            .keyboardShortcut("t", modifiers: .command)

            But<PERSON>("新建窗口") {
                // TODO: 实现新建窗口
            }
            .keyboardShortcut("n", modifiers: .command)

            Divider()

            But<PERSON>("关闭标签页") {
                // TODO: 实现关闭标签页
            }
            .keyboardShortcut("w", modifiers: .command)
        }

        CommandGroup(after: .undoRedo) {
            But<PERSON>("重新加载") {
                // TODO: 实现重新加载
            }
            .keyboardShortcut("r", modifiers: .command)

            But<PERSON>("强制重新加载") {
                // TODO: 实现强制重新加载
            }
            .keyboardShortcut("r", modifiers: [.command, .shift])
        }

        CommandMenu("历史记录") {
            Button("显示历史记录") {
                // TODO: 显示历史记录
            }
            .keyboardShortcut("y", modifiers: .command)

            Button("后退") {
                // TODO: 后退
            }
            .keyboardShortcut(.leftArrow, modifiers: .command)

            Button("前进") {
                // TODO: 前进
            }
            .keyboardShortcut(.rightArrow, modifiers: .command)
        }

        CommandMenu("书签") {
            Button("添加书签") {
                // TODO: 添加书签
            }
            .keyboardShortcut("d", modifiers: .command)

            Button("书签管理器") {
                // TODO: 显示书签管理器
            }
            .keyboardShortcut("b", modifiers: [.command, .option])

            Button("显示/隐藏书签栏") {
                // TODO: 切换书签栏
            }
            .keyboardShortcut("b", modifiers: [.command, .shift])
        }

        CommandMenu("窗口") {
            Button("下载管理器") {
                // TODO: 显示下载管理器
            }
            .keyboardShortcut("j", modifiers: [.command, .shift])

            Button("开发者工具") {
                // TODO: 显示开发者工具
            }
            .keyboardShortcut("i", modifiers: [.command, .option])
        }

        CommandGroup(replacing: .help) {
            Button("WebOS 帮助") {
                // TODO: 显示帮助
            }
        }
    }
}
