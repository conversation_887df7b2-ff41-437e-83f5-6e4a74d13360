//
//  TabManager.swift
//  webos
//
//  Created by lincoo on 7/3/25.
//

import SwiftUI
import Foundation

class Tab: ObservableObject, Identifiable {
    let id = UUID()
    @Published var url: String
    @Published var title: String
    @Published var canGoBack: Bool = false
    @Published var canGoForward: Bool = false
    @Published var isLoading: Bool = false
    @Published var estimatedProgress: Double = 0.0
    @Published var favicon: NSImage?
    
    init(url: String = "webos://start", title: String = "新标签页") {
        self.url = url
        self.title = title
    }
}

class TabManager: ObservableObject {
    @Published var tabs: [Tab] = []
    @Published var activeTabIndex: Int = 0
    
    var activeTab: Tab? {
        guard activeTabIndex >= 0 && activeTabIndex < tabs.count else { return nil }
        return tabs[activeTabIndex]
    }
    
    init() {
        // 创建初始标签页
        addNewTab()
    }
    
    func addNewTab(url: String = "webos://start", title: String = "新标签页") {
        let newTab = Tab(url: url, title: title)
        tabs.append(newTab)
        activeTabIndex = tabs.count - 1
    }
    
    func closeTab(at index: Int) {
        guard index >= 0 && index < tabs.count else { return }
        
        tabs.remove(at: index)
        
        if tabs.isEmpty {
            // 如果没有标签页了，创建一个新的
            addNewTab()
        } else if activeTabIndex >= tabs.count {
            // 如果当前活动标签页被删除，选择最后一个标签页
            activeTabIndex = tabs.count - 1
        } else if index < activeTabIndex {
            // 如果删除的标签页在当前活动标签页之前，调整索引
            activeTabIndex -= 1
        }
    }
    
    func closeTab(_ tab: Tab) {
        if let index = tabs.firstIndex(where: { $0.id == tab.id }) {
            closeTab(at: index)
        }
    }
    
    func selectTab(at index: Int) {
        guard index >= 0 && index < tabs.count else { return }
        activeTabIndex = index
    }
    
    func selectTab(_ tab: Tab) {
        if let index = tabs.firstIndex(where: { $0.id == tab.id }) {
            selectTab(at: index)
        }
    }
    
    func moveTab(from source: IndexSet, to destination: Int) {
        tabs.move(fromOffsets: source, toOffset: destination)
        
        // 更新活动标签页索引
        if let sourceIndex = source.first {
            if sourceIndex == activeTabIndex {
                // 移动的是当前活动标签页
                if destination > sourceIndex {
                    activeTabIndex = destination - 1
                } else {
                    activeTabIndex = destination
                }
            } else if sourceIndex < activeTabIndex && destination > activeTabIndex {
                activeTabIndex -= 1
            } else if sourceIndex > activeTabIndex && destination <= activeTabIndex {
                activeTabIndex += 1
            }
        }
    }
}

struct TabBar: View {
    @ObservedObject var tabManager: TabManager
    
    var body: some View {
        HStack(spacing: 0) {
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 0) {
                    ForEach(Array(tabManager.tabs.enumerated()), id: \.element.id) { index, tab in
                        TabView(
                            tab: tab,
                            isActive: index == tabManager.activeTabIndex,
                            onSelect: {
                                tabManager.selectTab(at: index)
                            },
                            onClose: {
                                tabManager.closeTab(at: index)
                            }
                        )
                    }
                }
            }
            
            // 新建标签页按钮
            Button(action: {
                tabManager.addNewTab()
            }) {
                Image(systemName: "plus")
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(.secondary)
            }
            .buttonStyle(PlainButtonStyle())
            .frame(width: 28, height: 28)
            .background(
                Circle()
                    .fill(Color(NSColor.controlBackgroundColor))
            )
            .padding(.trailing, 8)
        }
        .padding(.horizontal, 8)
        .padding(.vertical, 4)
        .background(Color(NSColor.windowBackgroundColor))
    }
}

struct TabView: View {
    @ObservedObject var tab: Tab
    let isActive: Bool
    let onSelect: () -> Void
    let onClose: () -> Void
    
    @State private var isHovering = false
    
    var body: some View {
        HStack(spacing: 6) {
            // Favicon
            if let favicon = tab.favicon {
                Image(nsImage: favicon)
                    .resizable()
                    .frame(width: 16, height: 16)
            } else {
                Image(systemName: "globe")
                    .font(.system(size: 12))
                    .foregroundColor(.secondary)
            }
            
            // 标题
            Text(tab.title.isEmpty ? "新标签页" : tab.title)
                .font(.system(size: 12))
                .foregroundColor(isActive ? .primary : .secondary)
                .lineLimit(1)
                .truncationMode(.tail)
            
            // 关闭按钮
            if isHovering || isActive {
                Button(action: onClose) {
                    Image(systemName: "xmark")
                        .font(.system(size: 10, weight: .medium))
                        .foregroundColor(.secondary)
                }
                .buttonStyle(PlainButtonStyle())
                .frame(width: 16, height: 16)
                .background(
                    Circle()
                        .fill(Color(NSColor.controlBackgroundColor))
                        .opacity(0.8)
                )
            }
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 6)
        .frame(minWidth: 120, maxWidth: 200)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(isActive ? Color(NSColor.controlBackgroundColor) : Color.clear)
        )
        .overlay(
            RoundedRectangle(cornerRadius: 8)
                .stroke(isActive ? Color(NSColor.separatorColor) : Color.clear, lineWidth: 1)
        )
        .onTapGesture {
            onSelect()
        }
        .onHover { hovering in
            isHovering = hovering
        }
    }
}
