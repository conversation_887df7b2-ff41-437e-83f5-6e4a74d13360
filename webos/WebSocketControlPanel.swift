//
//  WebSocketControlPanel.swift
//  webos
//
//  Created by lincoo on 7/3/25.
//

import SwiftUI

struct WebSocketControlPanel: View {
    @ObservedObject var webSocketManager: WebSocketManager
    @Environment(\.presentationMode) var presentationMode
    
    @State private var messageToSend = ""
    @State private var showingMessageHistory = false
    
    var body: some View {
        VStack(spacing: 0) {
            // 标题栏
            HStack {
                Text("WebSocket 控制面板")
                    .font(.title2)
                    .fontWeight(.semibold)
                
                Spacer()
                
                // 连接状态指示器
                HStack(spacing: 8) {
                    Circle()
                        .fill(webSocketManager.isConnected ? Color.green : Color.red)
                        .frame(width: 8, height: 8)
                    
                    Text(webSocketManager.connectionStatus)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Button("完成") {
                    presentationMode.wrappedValue.dismiss()
                }
            }
            .padding()
            
            Divider()
            
            // 连接控制
            VStack(spacing: 16) {
                HStack {
                    Text("服务器地址:")
                        .font(.headline)
                    
                    Text("ws://localhost:8002/scp")
                        .font(.system(.body, design: .monospaced))
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(Color(NSColor.controlBackgroundColor))
                        .cornerRadius(4)
                    
                    Spacer()
                    
                    if webSocketManager.isConnected {
                        Button("断开连接") {
                            webSocketManager.disconnect()
                        }
                        .foregroundColor(.red)
                    } else {
                        Button("连接") {
                            webSocketManager.connect()
                        }
                        .foregroundColor(.blue)
                    }
                }
                
                // 最后收到的消息
                if !webSocketManager.lastMessage.isEmpty {
                    VStack(alignment: .leading, spacing: 8) {
                        Text("最后收到的消息:")
                            .font(.headline)
                        
                        ScrollView {
                            Text(webSocketManager.lastMessage)
                                .font(.system(.body, design: .monospaced))
                                .frame(maxWidth: .infinity, alignment: .leading)
                                .padding(8)
                                .background(Color(NSColor.controlBackgroundColor))
                                .cornerRadius(8)
                        }
                        .frame(height: 100)
                    }
                }
            }
            .padding()
            
            Divider()
            
            // 发送消息
            VStack(spacing: 12) {
                HStack {
                    Text("发送消息:")
                        .font(.headline)
                    
                    Spacer()
                    
                    Button("消息历史") {
                        showingMessageHistory = true
                    }
                    .font(.caption)
                }
                
                HStack {
                    TextField("输入要发送的消息", text: $messageToSend, onCommit: {
                        sendMessage()
                    })
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    
                    Button("发送") {
                        sendMessage()
                    }
                    .disabled(!webSocketManager.isConnected || messageToSend.isEmpty)
                }
                
                // 快速指令按钮
                LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 3), spacing: 8) {
                    QuickCommandButton(title: "重新加载", command: """
                        {"type": "reload"}
                        """) {
                        webSocketManager.sendMessage($0)
                    }
                    
                    QuickCommandButton(title: "后退", command: """
                        {"type": "back"}
                        """) {
                        webSocketManager.sendMessage($0)
                    }
                    
                    QuickCommandButton(title: "前进", command: """
                        {"type": "forward"}
                        """) {
                        webSocketManager.sendMessage($0)
                    }
                    
                    QuickCommandButton(title: "新标签页", command: """
                        {"type": "new_tab"}
                        """) {
                        webSocketManager.sendMessage($0)
                    }
                    
                    QuickCommandButton(title: "关闭标签页", command: """
                        {"type": "close_tab"}
                        """) {
                        webSocketManager.sendMessage($0)
                    }
                    
                    QuickCommandButton(title: "访问 Apple", command: """
                        {"type": "navigate", "payload": {"url": "https://www.apple.com"}}
                        """) {
                        webSocketManager.sendMessage($0)
                    }
                }
            }
            .padding()
            
            Spacer()
        }
        .frame(width: 600, height: 500)
        .sheet(isPresented: $showingMessageHistory) {
            WebSocketMessageHistoryView(webSocketManager: webSocketManager)
        }
    }
    
    private func sendMessage() {
        guard !messageToSend.isEmpty else { return }
        webSocketManager.sendMessage(messageToSend)
        messageToSend = ""
    }
}

struct QuickCommandButton: View {
    let title: String
    let command: String
    let onSend: (String) -> Void
    
    var body: some View {
        Button(action: {
            onSend(command)
        }) {
            Text(title)
                .font(.caption)
                .padding(.horizontal, 8)
                .padding(.vertical, 4)
                .frame(maxWidth: .infinity)
                .background(Color(NSColor.controlAccentColor).opacity(0.1))
                .cornerRadius(6)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct WebSocketMessageHistoryView: View {
    @ObservedObject var webSocketManager: WebSocketManager
    @Environment(\.presentationMode) var presentationMode
    
    var body: some View {
        VStack(spacing: 0) {
            // 标题栏
            HStack {
                Text("消息历史")
                    .font(.title2)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Button("清除历史") {
                    webSocketManager.messageHistory.removeAll()
                }
                
                Button("完成") {
                    presentationMode.wrappedValue.dismiss()
                }
            }
            .padding()
            
            Divider()
            
            // 消息列表
            ScrollView {
                LazyVStack(spacing: 0) {
                    ForEach(webSocketManager.messageHistory) { message in
                        WebSocketMessageRow(message: message)
                        
                        if message.id != webSocketManager.messageHistory.last?.id {
                            Divider()
                                .padding(.horizontal)
                        }
                    }
                }
            }
        }
        .frame(width: 700, height: 500)
    }
}

struct WebSocketMessageRow: View {
    let message: WebSocketMessage
    
    private var timeFormatter: DateFormatter {
        let formatter = DateFormatter()
        formatter.timeStyle = .medium
        formatter.dateStyle = .none
        return formatter
    }
    
    var body: some View {
        HStack(alignment: .top, spacing: 12) {
            // 类型指示器
            Circle()
                .fill(colorForMessageType(message.type))
                .frame(width: 8, height: 8)
                .padding(.top, 6)
            
            VStack(alignment: .leading, spacing: 4) {
                HStack {
                    Text(message.sender)
                        .font(.system(size: 12, weight: .semibold))
                        .foregroundColor(colorForMessageType(message.type))
                    
                    Spacer()
                    
                    Text(timeFormatter.string(from: message.timestamp))
                        .font(.system(size: 10))
                        .foregroundColor(.secondary)
                }
                
                Text(message.content)
                    .font(.system(size: 12, design: .monospaced))
                    .foregroundColor(.primary)
                    .textSelection(.enabled)
            }
            
            Spacer()
        }
        .padding(.horizontal)
        .padding(.vertical, 8)
    }
    
    private func colorForMessageType(_ type: WebSocketMessageType) -> Color {
        switch type {
        case .sent:
            return .blue
        case .received:
            return .green
        case .system:
            return .gray
        case .error:
            return .red
        case .warning:
            return .orange
        case .info:
            return .cyan
        case .command:
            return .purple
        }
    }
}

#Preview {
    WebSocketControlPanel(webSocketManager: WebSocketManager())
}
