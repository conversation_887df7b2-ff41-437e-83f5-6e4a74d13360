//
//  WebView.swift
//  webos
//
//  Created by lincoo on 7/3/25.
//

import SwiftUI
import WebKit

struct WebView: NSViewRepresentable {
    @Binding var url: String
    @Binding var canGoBack: Bo<PERSON>
    @Binding var canGoForward: <PERSON><PERSON>
    @Binding var isLoading: Bool
    @Binding var title: String
    @Binding var estimatedProgress: Double

    var downloadManager: DownloadManager?

    let webView = WKWebView()
    
    func makeNSView(context: Context) -> WKWebView {
        webView.navigationDelegate = context.coordinator
        webView.uiDelegate = context.coordinator
        
        // 启用开发者工具
        if #available(macOS 13.3, *) {
            webView.isInspectable = true
        }
        
        // 设置用户代理
        webView.customUserAgent = "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Safari/605.1.15"
        
        // 添加观察者
        webView.addObserver(context.coordinator, forKeyPath: #keyPath(WKWebView.canGoBack), options: .new, context: nil)
        webView.addObserver(context.coordinator, forKeyPath: #keyPath(WKWebView.canGoForward), options: .new, context: nil)
        webView.addObserver(context.coordinator, forKeyPath: #keyPath(WKWebView.isLoading), options: .new, context: nil)
        webView.addObserver(context.coordinator, forKeyPath: #keyPath(WKWebView.title), options: .new, context: nil)
        webView.addObserver(context.coordinator, forKeyPath: #keyPath(WKWebView.estimatedProgress), options: .new, context: nil)
        
        return webView
    }
    
    func updateNSView(_ nsView: WKWebView, context: Context) {
        if let url = URL(string: url), nsView.url != url {
            let request = URLRequest(url: url)
            nsView.load(request)
        }
    }
    
    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }
    
    class Coordinator: NSObject, WKNavigationDelegate, WKUIDelegate {
        let parent: WebView
        
        init(_ parent: WebView) {
            self.parent = parent
        }
        
        // MARK: - KVO
        override func observeValue(forKeyPath keyPath: String?, of object: Any?, change: [NSKeyValueChangeKey : Any]?, context: UnsafeMutableRawPointer?) {
            DispatchQueue.main.async {
                self.parent.canGoBack = self.parent.webView.canGoBack
                self.parent.canGoForward = self.parent.webView.canGoForward
                self.parent.isLoading = self.parent.webView.isLoading
                self.parent.title = self.parent.webView.title ?? ""
                self.parent.estimatedProgress = self.parent.webView.estimatedProgress
            }
        }
        
        // MARK: - WKNavigationDelegate
        func webView(_ webView: WKWebView, didStartProvisionalNavigation navigation: WKNavigation!) {
            DispatchQueue.main.async {
                self.parent.isLoading = true
            }
        }
        
        func webView(_ webView: WKWebView, didFinish navigation: WKNavigation!) {
            DispatchQueue.main.async {
                self.parent.isLoading = false
                if let url = webView.url?.absoluteString {
                    self.parent.url = url
                }
            }
        }
        
        func webView(_ webView: WKWebView, didFail navigation: WKNavigation!, withError error: Error) {
            DispatchQueue.main.async {
                self.parent.isLoading = false
            }
        }
        
        func webView(_ webView: WKWebView, decidePolicyFor navigationAction: WKNavigationAction, decisionHandler: @escaping (WKNavigationActionPolicy) -> Void) {
            decisionHandler(.allow)
        }
        
        // MARK: - WKUIDelegate
        func webView(_ webView: WKWebView, createWebViewWith configuration: WKWebViewConfiguration, for navigationAction: WKNavigationAction, windowFeatures: WKWindowFeatures) -> WKWebView? {
            // 处理新窗口请求
            if navigationAction.targetFrame == nil {
                webView.load(navigationAction.request)
            }
            return nil
        }

        func webView(_ webView: WKWebView, decidePolicyFor navigationResponse: WKNavigationResponse, decisionHandler: @escaping (WKNavigationResponsePolicy) -> Void) {
            // 检查是否是下载文件
            if let mimeType = navigationResponse.response.mimeType,
               !["text/html", "text/plain", "application/json", "text/css", "text/javascript", "application/javascript"].contains(mimeType) {

                // 开始下载
                if let url = navigationResponse.response.url?.absoluteString,
                   let fileName = navigationResponse.response.suggestedFilename {
                    parent.downloadManager?.startDownload(url: url, fileName: fileName)
                }

                decisionHandler(.cancel)
                return
            }

            decisionHandler(.allow)
        }
        
        deinit {
            parent.webView.removeObserver(self, forKeyPath: #keyPath(WKWebView.canGoBack))
            parent.webView.removeObserver(self, forKeyPath: #keyPath(WKWebView.canGoForward))
            parent.webView.removeObserver(self, forKeyPath: #keyPath(WKWebView.isLoading))
            parent.webView.removeObserver(self, forKeyPath: #keyPath(WKWebView.title))
            parent.webView.removeObserver(self, forKeyPath: #keyPath(WKWebView.estimatedProgress))
        }
    }
    
    // MARK: - Public Methods
    func goBack() {
        webView.goBack()
    }
    
    func goForward() {
        webView.goForward()
    }
    
    func reload() {
        webView.reload()
    }
    
    func stopLoading() {
        webView.stopLoading()
    }
}
