//
//  HistoryManager.swift
//  webos
//
//  Created by lincoo on 7/3/25.
//

import SwiftUI
import Foundation

struct HistoryItem: Identifiable, Codable {
    let id = UUID()
    var title: String
    var url: String
    var visitDate: Date
    var visitCount: Int
    
    init(title: String, url: String, visitDate: Date = Date(), visitCount: Int = 1) {
        self.title = title
        self.url = url
        self.visitDate = visitDate
        self.visitCount = visitCount
    }
}

class HistoryManager: ObservableObject {
    @Published var historyItems: [HistoryItem] = []
    
    private let historyKey = "BrowsingHistory"
    private let maxHistoryItems = 1000
    
    init() {
        loadHistory()
    }
    
    func addHistoryItem(title: String, url: String) {
        // 检查是否已存在相同的URL
        if let existingIndex = historyItems.firstIndex(where: { $0.url == url }) {
            // 更新现有项目
            historyItems[existingIndex].visitDate = Date()
            historyItems[existingIndex].visitCount += 1
            historyItems[existingIndex].title = title
        } else {
            // 添加新项目
            let newItem = HistoryItem(title: title, url: url)
            historyItems.insert(newItem, at: 0)
        }
        
        // 限制历史记录数量
        if historyItems.count > maxHistoryItems {
            historyItems = Array(historyItems.prefix(maxHistoryItems))
        }
        
        saveHistory()
    }
    
    func removeHistoryItem(_ item: HistoryItem) {
        historyItems.removeAll { $0.id == item.id }
        saveHistory()
    }
    
    func clearHistory() {
        historyItems.removeAll()
        saveHistory()
    }
    
    func clearHistoryForDate(_ date: Date) {
        let calendar = Calendar.current
        historyItems.removeAll { item in
            calendar.isDate(item.visitDate, inSameDayAs: date)
        }
        saveHistory()
    }
    
    func getHistoryGroupedByDate() -> [(Date, [HistoryItem])] {
        let calendar = Calendar.current
        let grouped = Dictionary(grouping: historyItems) { item in
            calendar.startOfDay(for: item.visitDate)
        }
        
        return grouped.sorted { $0.key > $1.key }.map { (date, items) in
            (date, items.sorted { $0.visitDate > $1.visitDate })
        }
    }
    
    func searchHistory(query: String) -> [HistoryItem] {
        guard !query.isEmpty else { return historyItems }
        
        return historyItems.filter { item in
            item.title.localizedCaseInsensitiveContains(query) ||
            item.url.localizedCaseInsensitiveContains(query)
        }
    }
    
    private func saveHistory() {
        if let data = try? JSONEncoder().encode(historyItems) {
            UserDefaults.standard.set(data, forKey: historyKey)
        }
    }
    
    private func loadHistory() {
        if let data = UserDefaults.standard.data(forKey: historyKey),
           let savedHistory = try? JSONDecoder().decode([HistoryItem].self, from: data) {
            historyItems = savedHistory
        }
    }
}

struct HistoryView: View {
    @ObservedObject var historyManager: HistoryManager
    @Environment(\.presentationMode) var presentationMode
    
    @State private var searchText = ""
    
    let onNavigate: (String) -> Void
    
    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            // 标题栏
            HStack {
                Text("历史记录")
                    .font(.title2)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Button("清除历史记录") {
                    historyManager.clearHistory()
                }
                .foregroundColor(.red)
                
                Button("完成") {
                    presentationMode.wrappedValue.dismiss()
                }
            }
            .padding()
            
            // 搜索栏
            HStack {
                Image(systemName: "magnifyingglass")
                    .foregroundColor(.secondary)
                
                TextField("搜索历史记录", text: $searchText)
                    .textFieldStyle(PlainTextFieldStyle())
                
                if !searchText.isEmpty {
                    Button(action: {
                        searchText = ""
                    }) {
                        Image(systemName: "xmark.circle.fill")
                            .foregroundColor(.secondary)
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(Color(NSColor.controlBackgroundColor))
            .cornerRadius(8)
            .padding(.horizontal)
            
            Divider()
                .padding(.top)
            
            // 历史记录列表
            ScrollView {
                LazyVStack(alignment: .leading, spacing: 0) {
                    if searchText.isEmpty {
                        // 按日期分组显示
                        ForEach(historyManager.getHistoryGroupedByDate(), id: \.0) { date, items in
                            HistoryDateSection(
                                date: date,
                                items: items,
                                onNavigate: onNavigate,
                                onRemove: { item in
                                    historyManager.removeHistoryItem(item)
                                },
                                onClearDate: {
                                    historyManager.clearHistoryForDate(date)
                                }
                            )
                        }
                    } else {
                        // 搜索结果
                        let searchResults = historyManager.searchHistory(query: searchText)
                        ForEach(searchResults) { item in
                            HistoryItemRow(
                                item: item,
                                onNavigate: onNavigate,
                                onRemove: {
                                    historyManager.removeHistoryItem(item)
                                }
                            )
                            .padding(.horizontal)
                        }
                    }
                }
            }
        }
        .frame(width: 600, height: 500)
    }
}

struct HistoryDateSection: View {
    let date: Date
    let items: [HistoryItem]
    let onNavigate: (String) -> Void
    let onRemove: (HistoryItem) -> Void
    let onClearDate: () -> Void
    
    private var dateFormatter: DateFormatter {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .none
        return formatter
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            HStack {
                Text(dateFormatter.string(from: date))
                    .font(.headline)
                    .foregroundColor(.primary)
                
                Spacer()
                
                Button("清除") {
                    onClearDate()
                }
                .font(.caption)
                .foregroundColor(.red)
            }
            .padding(.horizontal)
            .padding(.vertical, 8)
            .background(Color(NSColor.controlBackgroundColor))
            
            ForEach(items) { item in
                HistoryItemRow(
                    item: item,
                    onNavigate: onNavigate,
                    onRemove: { onRemove(item) }
                )
                .padding(.horizontal)
            }
        }
    }
}

struct HistoryItemRow: View {
    let item: HistoryItem
    let onNavigate: (String) -> Void
    let onRemove: () -> Void
    
    @State private var isHovering = false
    
    private var timeFormatter: DateFormatter {
        let formatter = DateFormatter()
        formatter.timeStyle = .short
        return formatter
    }
    
    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 2) {
                Text(item.title.isEmpty ? "无标题" : item.title)
                    .font(.system(size: 14))
                    .foregroundColor(.primary)
                    .lineLimit(1)
                
                HStack {
                    Text(item.url)
                        .font(.system(size: 12))
                        .foregroundColor(.secondary)
                        .lineLimit(1)
                    
                    Spacer()
                    
                    Text(timeFormatter.string(from: item.visitDate))
                        .font(.system(size: 12))
                        .foregroundColor(.secondary)
                }
            }
            
            Spacer()
            
            if isHovering {
                Button(action: onRemove) {
                    Image(systemName: "xmark")
                        .font(.system(size: 12))
                        .foregroundColor(.secondary)
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
        .padding(.vertical, 8)
        .background(
            Rectangle()
                .fill(isHovering ? Color(NSColor.controlAccentColor).opacity(0.1) : Color.clear)
        )
        .onTapGesture {
            onNavigate(item.url)
        }
        .onHover { hovering in
            isHovering = hovering
        }
        .contextMenu {
            Button("在新标签页中打开") {
                // TODO: 在新标签页中打开
            }
            
            Button("复制链接") {
                NSPasteboard.general.clearContents()
                NSPasteboard.general.setString(item.url, forType: .string)
            }
            
            Divider()
            
            Button("从历史记录中删除") {
                onRemove()
            }
        }
    }
}
