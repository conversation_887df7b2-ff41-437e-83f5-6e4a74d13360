//
//  ContentView.swift
//  webos
//
//  Created by lincoo on 7/3/25.
//

import SwiftUI

struct ContentView: View {
    @StateObject private var tabManager = TabManager()
    @StateObject private var bookmarkManager = BookmarkManager()
    @StateObject private var historyManager = HistoryManager()
    @StateObject private var downloadManager = DownloadManager()

    @State private var showBookmarkManager = false
    @State private var showHistoryView = false
    @State private var showDownloadManager = false

    var body: some View {
        VStack(spacing: 0) {
            // 标签栏
            TabBar(tabManager: tabManager)

            // 导航栏
            if let activeTab = tabManager.activeTab {
                NavigationBar(
                    url: Binding(
                        get: { activeTab.url },
                        set: { newURL in
                            activeTab.url = newURL
                            navigateToURL(newURL)
                        }
                    ),
                    canGoBack: Binding(
                        get: { activeTab.canGoBack },
                        set: { _ in }
                    ),
                    canGoForward: Binding(
                        get: { activeTab.canGoForward },
                        set: { _ in }
                    ),
                    isLoading: Binding(
                        get: { activeTab.isLoading },
                        set: { _ in }
                    ),
                    estimatedProgress: Binding(
                        get: { activeTab.estimatedProgress },
                        set: { _ in }
                    ),
                    onGoBack: {
                        getCurrentWebView()?.goBack()
                    },
                    onGoForward: {
                        getCurrentWebView()?.goForward()
                    },
                    onReload: {
                        getCurrentWebView()?.reload()
                    },
                    onStopLoading: {
                        getCurrentWebView()?.stopLoading()
                    },
                    onNavigate: { url in
                        navigateToURL(url)
                    }
                )
            }

            // 工具栏
            ToolbarView(
                tabManager: tabManager,
                bookmarkManager: bookmarkManager,
                historyManager: historyManager,
                downloadManager: downloadManager,
                showBookmarkManager: $showBookmarkManager,
                showHistoryView: $showHistoryView,
                showDownloadManager: $showDownloadManager,
                onNavigate: { url in
                    navigateToURL(url)
                }
            )

            // 书签栏
            BookmarkBar(bookmarkManager: bookmarkManager) { url in
                navigateToURL(url)
            }

            // 主要内容区域
            ZStack {
                if let activeTab = tabManager.activeTab {
                    if activeTab.url == "webos://start" {
                        // 显示启动页面
                        StartPageView(
                            bookmarkManager: bookmarkManager,
                            historyManager: historyManager,
                            onNavigate: { url in
                                navigateToURL(url)
                            }
                        )
                        .id(activeTab.id)
                    } else {
                        // 显示网页
                        WebView(
                            url: Binding(
                                get: { activeTab.url },
                                set: { newURL in
                                    activeTab.url = newURL
                                    // 添加到历史记录
                                    if !newURL.hasPrefix("webos://") {
                                        historyManager.addHistoryItem(title: activeTab.title, url: newURL)
                                    }
                                }
                            ),
                            canGoBack: Binding(
                                get: { activeTab.canGoBack },
                                set: { activeTab.canGoBack = $0 }
                            ),
                            canGoForward: Binding(
                                get: { activeTab.canGoForward },
                                set: { activeTab.canGoForward = $0 }
                            ),
                            isLoading: Binding(
                                get: { activeTab.isLoading },
                                set: { activeTab.isLoading = $0 }
                            ),
                            title: Binding(
                                get: { activeTab.title },
                                set: { activeTab.title = $0 }
                            ),
                            estimatedProgress: Binding(
                                get: { activeTab.estimatedProgress },
                                set: { activeTab.estimatedProgress = $0 }
                            ),
                            downloadManager: downloadManager
                        )
                        .id(activeTab.id) // 确保每个标签页有独立的WebView
                    }
                }
            }
        }
        .sheet(isPresented: $showBookmarkManager) {
            BookmarkManagerView(bookmarkManager: bookmarkManager)
        }
        .sheet(isPresented: $showHistoryView) {
            HistoryView(historyManager: historyManager) { url in
                navigateToURL(url)
                showHistoryView = false
            }
        }
        .sheet(isPresented: $showDownloadManager) {
            DownloadManagerView(downloadManager: downloadManager)
        }
        .onAppear {
            setupKeyboardShortcuts()
        }
    }

    private func navigateToURL(_ url: String) {
        guard let activeTab = tabManager.activeTab else { return }
        activeTab.url = url

        // 添加到历史记录
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            if !activeTab.title.isEmpty {
                historyManager.addHistoryItem(title: activeTab.title, url: url)
            }
        }
    }

    private func getCurrentWebView() -> WebView? {
        // 这里需要一个方法来获取当前的WebView实例
        // 由于SwiftUI的限制，我们需要通过其他方式来实现
        return nil
    }

    private func setupKeyboardShortcuts() {
        // 这里可以添加键盘快捷键的设置
        // 由于SwiftUI的限制，可能需要在App级别设置
    }
}

#Preview {
    ContentView()
        .frame(width: 1200, height: 800)
}
