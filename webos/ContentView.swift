//
//  ContentView.swift
//  webos
//
//  Created by lincoo on 7/3/25.
//

import SwiftUI

struct ContentView: View {
    @StateObject private var tabManager = TabManager()
    @StateObject private var bookmarkManager = BookmarkManager()
    @StateObject private var historyManager = HistoryManager()
    @StateObject private var downloadManager = DownloadManager()
    @StateObject private var webSocketManager = WebSocketManager()

    @State private var showBookmarkManager = false
    @State private var showHistoryView = false
    @State private var showDownloadManager = false
    @State private var showWebSocketPanel = false

    var body: some View {
        VStack(spacing: 0) {
            // 标签栏
            TabBar(tabManager: tabManager)

            // 导航栏
            if let activeTab = tabManager.activeTab {
                NavigationBar(
                    url: Binding(
                        get: { activeTab.url },
                        set: { newURL in
                            activeTab.url = newURL
                            navigateToURL(newURL)
                        }
                    ),
                    canGoBack: Binding(
                        get: { activeTab.canGoBack },
                        set: { _ in }
                    ),
                    canGoForward: Binding(
                        get: { activeTab.canGoForward },
                        set: { _ in }
                    ),
                    isLoading: Binding(
                        get: { activeTab.isLoading },
                        set: { _ in }
                    ),
                    estimatedProgress: Binding(
                        get: { activeTab.estimatedProgress },
                        set: { _ in }
                    ),
                    onGoBack: {
                        getCurrentWebView()?.goBack()
                    },
                    onGoForward: {
                        getCurrentWebView()?.goForward()
                    },
                    onReload: {
                        getCurrentWebView()?.reload()
                    },
                    onStopLoading: {
                        getCurrentWebView()?.stopLoading()
                    },
                    onNavigate: { url in
                        navigateToURL(url)
                    }
                )
            }

            // 工具栏
            ToolbarView(
                tabManager: tabManager,
                bookmarkManager: bookmarkManager,
                historyManager: historyManager,
                downloadManager: downloadManager,
                webSocketManager: webSocketManager,
                showBookmarkManager: $showBookmarkManager,
                showHistoryView: $showHistoryView,
                showDownloadManager: $showDownloadManager,
                showWebSocketPanel: $showWebSocketPanel,
                onNavigate: { url in
                    navigateToURL(url)
                }
            )

            // 书签栏
            BookmarkBar(bookmarkManager: bookmarkManager) { url in
                navigateToURL(url)
            }

            // 主要内容区域
            ZStack {
                if let activeTab = tabManager.activeTab {
                    if activeTab.url == "webos://start" {
                        // 显示启动页面
                        StartPageView(
                            bookmarkManager: bookmarkManager,
                            historyManager: historyManager,
                            onNavigate: { url in
                                navigateToURL(url)
                            }
                        )
                        .id(activeTab.id)
                    } else {
                        // 显示网页
                        WebView(
                            url: Binding(
                                get: { activeTab.url },
                                set: { newURL in
                                    activeTab.url = newURL
                                    // 添加到历史记录
                                    if !newURL.hasPrefix("webos://") {
                                        historyManager.addHistoryItem(title: activeTab.title, url: newURL)
                                    }
                                }
                            ),
                            canGoBack: Binding(
                                get: { activeTab.canGoBack },
                                set: { activeTab.canGoBack = $0 }
                            ),
                            canGoForward: Binding(
                                get: { activeTab.canGoForward },
                                set: { activeTab.canGoForward = $0 }
                            ),
                            isLoading: Binding(
                                get: { activeTab.isLoading },
                                set: { activeTab.isLoading = $0 }
                            ),
                            title: Binding(
                                get: { activeTab.title },
                                set: { activeTab.title = $0 }
                            ),
                            estimatedProgress: Binding(
                                get: { activeTab.estimatedProgress },
                                set: { activeTab.estimatedProgress = $0 }
                            ),
                            downloadManager: downloadManager
                        )
                        .id(activeTab.id) // 确保每个标签页有独立的WebView
                    }
                }
            }
        }
        .sheet(isPresented: $showBookmarkManager) {
            BookmarkManagerView(bookmarkManager: bookmarkManager)
        }
        .sheet(isPresented: $showHistoryView) {
            HistoryView(historyManager: historyManager) { url in
                navigateToURL(url)
                showHistoryView = false
            }
        }
        .sheet(isPresented: $showDownloadManager) {
            DownloadManagerView(downloadManager: downloadManager)
        }
        .sheet(isPresented: $showWebSocketPanel) {
            WebSocketControlPanel(webSocketManager: webSocketManager)
        }
        .onAppear {
            setupKeyboardShortcuts()
            setupWebSocketNotifications()
            // 自动连接到 WebSocket 服务器
            webSocketManager.connect()
        }
    }

    private func navigateToURL(_ url: String) {
        guard let activeTab = tabManager.activeTab else { return }
        activeTab.url = url

        // 添加到历史记录
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            if !activeTab.title.isEmpty {
                historyManager.addHistoryItem(title: activeTab.title, url: url)
            }
        }
    }

    private func getCurrentWebView() -> WebView? {
        // 这里需要一个方法来获取当前的WebView实例
        // 由于SwiftUI的限制，我们需要通过其他方式来实现
        return nil
    }

    private func setupKeyboardShortcuts() {
        // 这里可以添加键盘快捷键的设置
        // 由于SwiftUI的限制，可能需要在App级别设置
    }

    private func setupWebSocketNotifications() {
        // 监听 WebSocket 指令通知
        NotificationCenter.default.addObserver(
            forName: .navigateToURL,
            object: nil,
            queue: .main
        ) { notification in
            if let url = notification.object as? String {
                navigateToURL(url)
            }
        }

        NotificationCenter.default.addObserver(
            forName: .reloadPage,
            object: nil,
            queue: .main
        ) { _ in
            getCurrentWebView()?.reload()
        }

        NotificationCenter.default.addObserver(
            forName: .goBack,
            object: nil,
            queue: .main
        ) { _ in
            getCurrentWebView()?.goBack()
        }

        NotificationCenter.default.addObserver(
            forName: .goForward,
            object: nil,
            queue: .main
        ) { _ in
            getCurrentWebView()?.goForward()
        }

        NotificationCenter.default.addObserver(
            forName: .newTab,
            object: nil,
            queue: .main
        ) { notification in
            if let url = notification.object as? String {
                tabManager.addNewTab(url: url)
            } else {
                tabManager.addNewTab()
            }
        }

        NotificationCenter.default.addObserver(
            forName: .closeTab,
            object: nil,
            queue: .main
        ) { _ in
            if let activeTab = tabManager.activeTab {
                tabManager.closeTab(activeTab)
            }
        }

        NotificationCenter.default.addObserver(
            forName: .executeScript,
            object: nil,
            queue: .main
        ) { notification in
            if let script = notification.object as? String {
                executeJavaScript(script)
            }
        }
    }

    private func executeJavaScript(_ script: String) {
        // 在当前活动的 WebView 中执行 JavaScript
        // 由于 SwiftUI 的限制，这里需要通过其他方式实现
        // 可以考虑使用 Coordinator 模式或者全局引用
        print("执行 JavaScript: \(script)")
    }
}

#Preview {
    ContentView()
        .frame(width: 1200, height: 800)
}
