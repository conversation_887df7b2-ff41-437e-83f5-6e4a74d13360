//
//  NavigationBar.swift
//  webos
//
//  Created by lincoo on 7/3/25.
//

import SwiftUI

struct NavigationBar: View {
    @Binding var url: String
    @Binding var canGoBack: Bool
    @Binding var canGoForward: Bool
    @Binding var isLoading: Bo<PERSON>
    @Binding var estimatedProgress: Double
    
    @State private var urlInput: String = ""
    @State private var isEditingURL: Bool = false
    
    let onGoBack: () -> Void
    let onGoForward: () -> Void
    let onReload: () -> Void
    let onStopLoading: () -> Void
    let onNavigate: (String) -> Void
    
    var body: some View {
        VStack(spacing: 0) {
            HStack(spacing: 8) {
                // 后退按钮
                Button(action: onGoBack) {
                    Image(systemName: "chevron.left")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(canGoBack ? .primary : .secondary)
                }
                .disabled(!canGoBack)
                .buttonStyle(NavigationButtonStyle())
                
                // 前进按钮
                Button(action: onGoForward) {
                    Image(systemName: "chevron.right")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(canGoForward ? .primary : .secondary)
                }
                .disabled(!canGoForward)
                .buttonStyle(NavigationButtonStyle())
                
                // 刷新/停止按钮
                Button(action: isLoading ? onStopLoading : onReload) {
                    Image(systemName: isLoading ? "xmark" : "arrow.clockwise")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.primary)
                }
                .buttonStyle(NavigationButtonStyle())
                
                // 地址栏
                HStack {
                    // 安全图标
                    Image(systemName: url.hasPrefix("https://") ? "lock.fill" : "lock.open.fill")
                        .font(.system(size: 12))
                        .foregroundColor(url.hasPrefix("https://") ? .green : .orange)
                    
                    // URL输入框
                    TextField("输入网址或搜索", text: $urlInput, onEditingChanged: { editing in
                        isEditingURL = editing
                        if editing {
                            urlInput = url
                        }
                    }, onCommit: {
                        handleURLSubmit()
                    })
                    .textFieldStyle(PlainTextFieldStyle())
                    .font(.system(size: 14))
                    .onAppear {
                        urlInput = url
                    }
                    .onChange(of: url) { newURL in
                        if !isEditingURL {
                            urlInput = newURL
                        }
                    }
                    
                    // 清除按钮
                    if isEditingURL && !urlInput.isEmpty {
                        Button(action: {
                            urlInput = ""
                        }) {
                            Image(systemName: "xmark.circle.fill")
                                .font(.system(size: 12))
                                .foregroundColor(.secondary)
                        }
                        .buttonStyle(PlainButtonStyle())
                    }
                }
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(Color(NSColor.controlBackgroundColor))
                .cornerRadius(8)
                .overlay(
                    RoundedRectangle(cornerRadius: 8)
                        .stroke(Color(NSColor.separatorColor), lineWidth: 1)
                )
                
                // 菜单按钮
                Button(action: {
                    // TODO: 实现菜单功能
                }) {
                    Image(systemName: "ellipsis.circle")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.primary)
                }
                .buttonStyle(NavigationButtonStyle())
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            
            // 进度条
            if isLoading && estimatedProgress > 0 {
                ProgressView(value: estimatedProgress)
                    .progressViewStyle(LinearProgressViewStyle())
                    .frame(height: 2)
                    .animation(.easeInOut(duration: 0.2), value: estimatedProgress)
            }
        }
        .background(Color(NSColor.windowBackgroundColor))
    }
    
    private func handleURLSubmit() {
        isEditingURL = false
        let trimmedInput = urlInput.trimmingCharacters(in: .whitespacesAndNewlines)
        
        if trimmedInput.isEmpty {
            urlInput = url
            return
        }
        
        var finalURL = trimmedInput
        
        // 检查是否是有效的URL
        if !trimmedInput.contains(".") || trimmedInput.contains(" ") {
            // 如果不是URL，使用搜索引擎搜索
            let searchQuery = trimmedInput.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? ""
            finalURL = "https://www.google.com/search?q=\(searchQuery)"
        } else if !trimmedInput.hasPrefix("http://") && !trimmedInput.hasPrefix("https://") {
            // 如果是域名但没有协议，添加https://
            finalURL = "https://\(trimmedInput)"
        }
        
        onNavigate(finalURL)
    }
}

struct NavigationButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .frame(width: 28, height: 28)
            .background(
                Circle()
                    .fill(configuration.isPressed ? Color(NSColor.controlAccentColor).opacity(0.2) : Color.clear)
            )
            .scaleEffect(configuration.isPressed ? 0.95 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
    }
}

#Preview {
    NavigationBar(
        url: .constant("https://www.apple.com"),
        canGoBack: .constant(true),
        canGoForward: .constant(false),
        isLoading: .constant(false),
        estimatedProgress: .constant(0.5),
        onGoBack: {},
        onGoForward: {},
        onReload: {},
        onStopLoading: {},
        onNavigate: { _ in }
    )
    .frame(width: 800)
}
