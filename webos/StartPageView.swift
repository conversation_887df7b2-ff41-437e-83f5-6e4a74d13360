//
//  StartPageView.swift
//  webos
//
//  Created by lincoo on 7/3/25.
//

import SwiftUI

struct StartPageView: View {
    @ObservedObject var bookmarkManager: BookmarkManager
    @ObservedObject var historyManager: HistoryManager
    
    let onNavigate: (String) -> Void
    
    @State private var searchText = ""
    
    var body: some View {
        ScrollView {
            VStack(spacing: 32) {
                Spacer()
                    .frame(height: 60)
                
                // Logo 和标题
                VStack(spacing: 16) {
                    Image(systemName: "globe")
                        .font(.system(size: 80, weight: .thin))
                        .foregroundColor(.blue)
                    
                    Text("WebOS")
                        .font(.system(size: 48, weight: .thin, design: .default))
                        .foregroundColor(.primary)
                }
                
                // 搜索框
                VStack(spacing: 16) {
                    HStack {
                        Image(systemName: "magnifyingglass")
                            .foregroundColor(.secondary)
                            .font(.system(size: 16))
                        
                        TextField("搜索或输入网址", text: $searchText, onCommit: {
                            handleSearch()
                        })
                        .textFieldStyle(PlainTextFieldStyle())
                        .font(.system(size: 16))
                        
                        if !searchText.isEmpty {
                            Button(action: {
                                searchText = ""
                            }) {
                                Image(systemName: "xmark.circle.fill")
                                    .foregroundColor(.secondary)
                                    .font(.system(size: 16))
                            }
                            .buttonStyle(PlainButtonStyle())
                        }
                    }
                    .padding(.horizontal, 16)
                    .padding(.vertical, 12)
                    .background(
                        RoundedRectangle(cornerRadius: 25)
                            .fill(Color(NSColor.controlBackgroundColor))
                            .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
                    )
                    .frame(maxWidth: 600)
                    
                    // 快速搜索按钮
                    HStack(spacing: 16) {
                        QuickSearchButton(title: "Google", icon: "magnifyingglass") {
                            onNavigate("https://www.google.com")
                        }
                        
                        QuickSearchButton(title: "GitHub", icon: "chevron.left.forwardslash.chevron.right") {
                            onNavigate("https://github.com")
                        }
                        
                        QuickSearchButton(title: "Stack Overflow", icon: "questionmark.circle") {
                            onNavigate("https://stackoverflow.com")
                        }
                        
                        QuickSearchButton(title: "Apple", icon: "applelogo") {
                            onNavigate("https://www.apple.com")
                        }
                    }
                }
                
                // 快速访问区域
                HStack(alignment: .top, spacing: 40) {
                    // 常用书签
                    VStack(alignment: .leading, spacing: 16) {
                        Text("常用书签")
                            .font(.headline)
                            .foregroundColor(.primary)
                        
                        if bookmarkManager.bookmarkBarItems.isEmpty {
                            Text("暂无书签")
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                        } else {
                            VStack(alignment: .leading, spacing: 8) {
                                ForEach(bookmarkManager.bookmarkBarItems.prefix(6)) { bookmark in
                                    StartPageBookmarkItem(bookmark: bookmark, onNavigate: onNavigate)
                                }
                            }
                        }
                    }
                    .frame(maxWidth: 250, alignment: .leading)
                    
                    // 最近访问
                    VStack(alignment: .leading, spacing: 16) {
                        Text("最近访问")
                            .font(.headline)
                            .foregroundColor(.primary)
                        
                        let recentItems = Array(historyManager.historyItems.prefix(6))
                        if recentItems.isEmpty {
                            Text("暂无历史记录")
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                        } else {
                            VStack(alignment: .leading, spacing: 8) {
                                ForEach(recentItems) { item in
                                    StartPageHistoryItem(item: item, onNavigate: onNavigate)
                                }
                            }
                        }
                    }
                    .frame(maxWidth: 250, alignment: .leading)
                }
                
                Spacer()
            }
            .padding(.horizontal, 40)
        }
        .background(Color(NSColor.windowBackgroundColor))
    }
    
    private func handleSearch() {
        guard !searchText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else { return }
        
        let trimmedInput = searchText.trimmingCharacters(in: .whitespacesAndNewlines)
        var finalURL = trimmedInput
        
        // 检查是否是有效的URL
        if !trimmedInput.contains(".") || trimmedInput.contains(" ") {
            // 如果不是URL，使用搜索引擎搜索
            let searchQuery = trimmedInput.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? ""
            finalURL = "https://www.google.com/search?q=\(searchQuery)"
        } else if !trimmedInput.hasPrefix("http://") && !trimmedInput.hasPrefix("https://") {
            // 如果是域名但没有协议，添加https://
            finalURL = "https://\(trimmedInput)"
        }
        
        onNavigate(finalURL)
        searchText = ""
    }
}

struct QuickSearchButton: View {
    let title: String
    let icon: String
    let action: () -> Void
    
    @State private var isHovering = false
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 8) {
                Image(systemName: icon)
                    .font(.system(size: 24))
                    .foregroundColor(.blue)
                
                Text(title)
                    .font(.caption)
                    .foregroundColor(.primary)
            }
            .frame(width: 80, height: 80)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(isHovering ? Color(NSColor.controlAccentColor).opacity(0.1) : Color(NSColor.controlBackgroundColor))
                    .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
            )
        }
        .buttonStyle(PlainButtonStyle())
        .onHover { hovering in
            isHovering = hovering
        }
    }
}

struct StartPageBookmarkItem: View {
    let bookmark: Bookmark
    let onNavigate: (String) -> Void
    
    @State private var isHovering = false
    
    var body: some View {
        Button(action: {
            onNavigate(bookmark.url)
        }) {
            HStack(spacing: 12) {
                Image(systemName: "globe")
                    .font(.system(size: 16))
                    .foregroundColor(.blue)
                    .frame(width: 20)
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(bookmark.title)
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.primary)
                        .lineLimit(1)
                    
                    Text(bookmark.url)
                        .font(.system(size: 12))
                        .foregroundColor(.secondary)
                        .lineLimit(1)
                }
                
                Spacer()
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(isHovering ? Color(NSColor.controlAccentColor).opacity(0.1) : Color.clear)
            )
        }
        .buttonStyle(PlainButtonStyle())
        .onHover { hovering in
            isHovering = hovering
        }
    }
}

struct StartPageHistoryItem: View {
    let item: HistoryItem
    let onNavigate: (String) -> Void
    
    @State private var isHovering = false
    
    private var timeFormatter: DateFormatter {
        let formatter = DateFormatter()
        formatter.timeStyle = .short
        formatter.dateStyle = .none
        return formatter
    }
    
    var body: some View {
        Button(action: {
            onNavigate(item.url)
        }) {
            HStack(spacing: 12) {
                Image(systemName: "clock")
                    .font(.system(size: 16))
                    .foregroundColor(.orange)
                    .frame(width: 20)
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(item.title.isEmpty ? "无标题" : item.title)
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.primary)
                        .lineLimit(1)
                    
                    HStack {
                        Text(item.url)
                            .font(.system(size: 12))
                            .foregroundColor(.secondary)
                            .lineLimit(1)
                        
                        Spacer()
                        
                        Text(timeFormatter.string(from: item.visitDate))
                            .font(.system(size: 12))
                            .foregroundColor(.secondary)
                    }
                }
                
                Spacer()
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(isHovering ? Color(NSColor.controlAccentColor).opacity(0.1) : Color.clear)
            )
        }
        .buttonStyle(PlainButtonStyle())
        .onHover { hovering in
            isHovering = hovering
        }
    }
}

#Preview {
    StartPageView(
        bookmarkManager: BookmarkManager(),
        historyManager: HistoryManager(),
        onNavigate: { _ in }
    )
    .frame(width: 1000, height: 700)
}
