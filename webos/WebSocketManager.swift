//
//  WebSocketManager.swift
//  webos
//
//  Created by linco<PERSON> on 7/3/25.
//

import Foundation
import Network

protocol WebSocketManagerDelegate: AnyObject {
    func webSocketDidConnect()
    func webSocketDidDisconnect(error: Error?)
    func webSocketDidReceiveMessage(_ message: String)
    func webSocketDidReceiveData(_ data: Data)
}

class WebSocketManager: ObservableObject {
    @Published var isConnected = false
    @Published var connectionStatus = "未连接"
    @Published var lastMessage = ""
    @Published var messageHistory: [WebSocketMessage] = []
    
    private var webSocketTask: URLSessionWebSocketTask?
    private var urlSession: URLSession?
    private let serverURL = "ws://localhost:8002/scp"
    
    weak var delegate: WebSocketManagerDelegate?
    
    init() {
        setupURLSession()
    }
    
    private func setupURLSession() {
        let config = URLSessionConfiguration.default
        config.timeoutIntervalForRequest = 30
        config.timeoutIntervalForResource = 30
        urlSession = URLSession(configuration: config)
    }
    
    func connect() {
        guard let url = URL(string: serverURL) else {
            updateConnectionStatus("无效的服务器地址")
            return
        }
        
        disconnect() // 断开现有连接
        
        updateConnectionStatus("正在连接...")
        
        webSocketTask = urlSession?.webSocketTask(with: url)
        webSocketTask?.resume()
        
        // 开始监听消息
        receiveMessage()
        
        // 监听连接状态
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            if self.webSocketTask?.state == .running {
                self.isConnected = true
                self.updateConnectionStatus("已连接")
                self.delegate?.webSocketDidConnect()
                self.addMessage("系统", "WebSocket 连接成功", .system)
            } else {
                self.updateConnectionStatus("连接失败")
                self.addMessage("系统", "WebSocket 连接失败", .error)
            }
        }
    }
    
    func disconnect() {
        webSocketTask?.cancel(with: .goingAway, reason: nil)
        webSocketTask = nil
        isConnected = false
        updateConnectionStatus("已断开")
        delegate?.webSocketDidDisconnect(error: nil)
    }
    
    func sendMessage(_ message: String) {
        guard isConnected, let webSocketTask = webSocketTask else {
            addMessage("系统", "未连接到服务器", .error)
            return
        }
        
        let message = URLSessionWebSocketTask.Message.string(message)
        webSocketTask.send(message) { [weak self] error in
            DispatchQueue.main.async {
                if let error = error {
                    self?.addMessage("系统", "发送消息失败: \(error.localizedDescription)", .error)
                } else {
                    switch message {
                    case .string(let text):
                        self?.addMessage("发送", text, .sent)
                    case .data(let data):
                        self?.addMessage("发送", "二进制数据 (\(data.count) 字节)", .sent)
                    @unknown default:
                        self?.addMessage("发送", "未知消息类型", .sent)
                    }
                }
            }
        }
    }
    
    func sendData(_ data: Data) {
        guard isConnected, let webSocketTask = webSocketTask else {
            addMessage("系统", "未连接到服务器", .error)
            return
        }
        
        let message = URLSessionWebSocketTask.Message.data(data)
        webSocketTask.send(message) { [weak self] error in
            DispatchQueue.main.async {
                if let error = error {
                    self?.addMessage("系统", "发送数据失败: \(error.localizedDescription)", .error)
                } else {
                    self?.addMessage("发送", "二进制数据 (\(data.count) 字节)", .sent)
                }
            }
        }
    }
    
    private func receiveMessage() {
        webSocketTask?.receive { [weak self] result in
            switch result {
            case .success(let message):
                DispatchQueue.main.async {
                    self?.handleReceivedMessage(message)
                }
                // 继续监听下一条消息
                self?.receiveMessage()
                
            case .failure(let error):
                DispatchQueue.main.async {
                    self?.handleConnectionError(error)
                }
            }
        }
    }
    
    private func handleReceivedMessage(_ message: URLSessionWebSocketTask.Message) {
        switch message {
        case .string(let text):
            lastMessage = text
            addMessage("接收", text, .received)
            delegate?.webSocketDidReceiveMessage(text)
            
            // 处理特定指令
            handleCommand(text)
            
        case .data(let data):
            let dataString = "二进制数据 (\(data.count) 字节)"
            addMessage("接收", dataString, .received)
            delegate?.webSocketDidReceiveData(data)
            
        @unknown default:
            addMessage("系统", "收到未知类型消息", .error)
        }
    }
    
    private func handleCommand(_ command: String) {
        // 解析和处理来自服务器的指令
        do {
            if let data = command.data(using: .utf8),
               let json = try JSONSerialization.jsonObject(with: data) as? [String: Any] {
                
                let type = json["type"] as? String ?? ""
                let payload = json["payload"] as? [String: Any] ?? [:]
                
                switch type {
                case "navigate":
                    if let url = payload["url"] as? String {
                        handleNavigateCommand(url: url)
                    }
                    
                case "reload":
                    handleReloadCommand()
                    
                case "back":
                    handleBackCommand()
                    
                case "forward":
                    handleForwardCommand()
                    
                case "new_tab":
                    if let url = payload["url"] as? String {
                        handleNewTabCommand(url: url)
                    } else {
                        handleNewTabCommand(url: nil)
                    }
                    
                case "close_tab":
                    handleCloseTabCommand()
                    
                case "execute_script":
                    if let script = payload["script"] as? String {
                        handleExecuteScriptCommand(script: script)
                    }
                    
                default:
                    addMessage("系统", "未知指令类型: \(type)", .warning)
                }
            }
        } catch {
            // 如果不是 JSON 格式，作为普通文本处理
            addMessage("系统", "收到文本指令: \(command)", .info)
        }
    }
    
    private func handleNavigateCommand(url: String) {
        addMessage("指令", "导航到: \(url)", .command)
        NotificationCenter.default.post(name: .navigateToURL, object: url)
    }
    
    private func handleReloadCommand() {
        addMessage("指令", "重新加载页面", .command)
        NotificationCenter.default.post(name: .reloadPage, object: nil)
    }
    
    private func handleBackCommand() {
        addMessage("指令", "后退", .command)
        NotificationCenter.default.post(name: .goBack, object: nil)
    }
    
    private func handleForwardCommand() {
        addMessage("指令", "前进", .command)
        NotificationCenter.default.post(name: .goForward, object: nil)
    }
    
    private func handleNewTabCommand(url: String?) {
        let message = url != nil ? "新建标签页: \(url!)" : "新建标签页"
        addMessage("指令", message, .command)
        NotificationCenter.default.post(name: .newTab, object: url)
    }
    
    private func handleCloseTabCommand() {
        addMessage("指令", "关闭标签页", .command)
        NotificationCenter.default.post(name: .closeTab, object: nil)
    }
    
    private func handleExecuteScriptCommand(script: String) {
        addMessage("指令", "执行脚本: \(script)", .command)
        NotificationCenter.default.post(name: .executeScript, object: script)
    }
    
    private func handleConnectionError(_ error: Error) {
        isConnected = false
        updateConnectionStatus("连接错误: \(error.localizedDescription)")
        addMessage("系统", "连接错误: \(error.localizedDescription)", .error)
        delegate?.webSocketDidDisconnect(error: error)
        
        // 尝试重连
        DispatchQueue.main.asyncAfter(deadline: .now() + 5.0) {
            if !self.isConnected {
                self.addMessage("系统", "尝试重新连接...", .info)
                self.connect()
            }
        }
    }
    
    private func updateConnectionStatus(_ status: String) {
        connectionStatus = status
    }
    
    private func addMessage(_ sender: String, _ content: String, _ type: WebSocketMessageType) {
        let message = WebSocketMessage(
            id: UUID(),
            sender: sender,
            content: content,
            timestamp: Date(),
            type: type
        )
        messageHistory.append(message)
        
        // 限制历史记录数量
        if messageHistory.count > 100 {
            messageHistory.removeFirst()
        }
    }
    
    deinit {
        disconnect()
    }
}

// MARK: - 数据模型
struct WebSocketMessage: Identifiable {
    let id: UUID
    let sender: String
    let content: String
    let timestamp: Date
    let type: WebSocketMessageType
}

enum WebSocketMessageType {
    case sent
    case received
    case system
    case error
    case warning
    case info
    case command
}

// MARK: - 通知名称
extension Notification.Name {
    static let navigateToURL = Notification.Name("navigateToURL")
    static let reloadPage = Notification.Name("reloadPage")
    static let goBack = Notification.Name("goBack")
    static let goForward = Notification.Name("goForward")
    static let newTab = Notification.Name("newTab")
    static let closeTab = Notification.Name("closeTab")
    static let executeScript = Notification.Name("executeScript")
}
